<script setup lang="ts">
import invariant from 'tiny-invariant'
import { ref, toRaw, toRefs } from 'vue'
import {
  DropdownMenuContent,
  DropdownMenuPortal,
  DropdownMenuRoot,
  DropdownMenuTrigger,
} from 'reka-ui'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import { useDocumentStore } from '@js/stores/document'
import Translator from '@js/translator'
import type { DocumentSection } from '@js/model/document'
import type { DocumentSectionPlacement } from '@js/api/documentApi'
import type { HierarchicalSection } from '@js/helper/document/transformSectionsToHierarchy'

defineOptions({ inheritAttrs: false })

const emit = defineEmits<(event: 'saved') => void>()

const props = withDefaults(
  defineProps<{
    disabled?: boolean
    hierarchicalSection?: HierarchicalSection
  }>(),
  {
    disabled: false,
    hierarchicalSection: undefined,
  }
)

const open = ref(false)

const documentStore = useDocumentStore()
const { hierarchicalSection } = toRefs(props)

function getDropdownItemText(config: {
  placement: DocumentSectionPlacement
  referenceSectionId: DocumentSection['id']
}) {
  const referenceSection = documentStore.sections.find(
    (section) => section.id === config.referenceSectionId
  )
  invariant(referenceSection, 'Reference section must be defined')
  const sectionNumbering = documentStore.numberingBySection.get(referenceSection)

  if (config.placement === 'before') {
    const referenceSectionParent = documentStore.sectionToParentSection.get(referenceSection)
    if (!referenceSectionParent) {
      return `${Translator.trans('u2_structureddocument.before')} ${documentStore.numberingBySection.get(referenceSection)}`
    }

    return `${Translator.trans('u2_structureddocument.subsection_of')} ${toRaw(documentStore.numberingBySection.get(referenceSectionParent))}`
  }

  if (config.placement === 'subsection-of') {
    return `${Translator.trans('u2_structureddocument.subsection_of')} ${sectionNumbering}`
  }

  return `${Translator.trans('u2_structureddocument.after')} ${sectionNumbering}`
}
</script>
<template>
  <DropdownMenuRoot v-model:open="open" :modal="false">
    <DropdownMenuTrigger
      as="button"
      class="group block w-full outline-none"
      :disabled="disabled"
      v-bind="$attrs"
    >
      <div class="flex w-full items-center">
        <hr
          :class="[
            disabled
              ? 'group-hover:border-gray-400 group-focus-visible:border-gray-400'
              : 'group-hover:border-blue-200 group-focus-visible:border-blue-200',
            { 'border-blue-200': open },
          ]"
          class="border-t-2 border-dashed border-black/0 duration-300 ease-in-out group-hover:border-blue-200 group-focus-visible:border-blue-200"
        />

        <span
          :class="[disabled ? 'text-gray-500' : 'text-action', { 'opacity-100': open }]"
          class="inline-flex items-center gap-x-1 whitespace-nowrap px-3 py-2 opacity-0 duration-300 ease-in-out group-hover:opacity-100 group-focus-visible:opacity-100"
        >
          {{ Translator.trans('u2_structureddocument.add_section') }}
          <SvgIcon
            class="mt-0.5 shrink-0 transform transition duration-300 ease-in-out"
            :class="{ 'rotate-180': !open }"
            icon="arrow-up"
          />
        </span>

        <hr
          :class="[
            disabled
              ? 'group-hover:border-gray-400 group-focus-visible:border-gray-400'
              : 'group-hover:border-blue-200 group-focus-visible:border-blue-200',
            { 'border-blue-200': open },
          ]"
          class="border-t-2 border-dashed border-black/0 duration-300 ease-in-out"
        />
      </div>
    </DropdownMenuTrigger>
    <DropdownMenuPortal>
      <DropdownMenuContent
        class="mt-1 min-w-full whitespace-nowrap rounded bg-white text-left shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
      >
        <div class="inline-flex w-full flex-col p-1">
          <ButtonDropdownItem
            v-for="config in documentStore.getPossibleNewSectionConfigs(
              hierarchicalSection?.section
            )"
            :key="config.placement + config.referenceSectionId"
            icon="add"
            :text="getDropdownItemText(config)"
            @click="
              async () => {
                if (
                  await documentStore.createNewSection(config.placement, config.referenceSectionId)
                ) {
                  emit('saved')
                }
              }
            "
          />
        </div>
      </DropdownMenuContent>
    </DropdownMenuPortal>
  </DropdownMenuRoot>
</template>
