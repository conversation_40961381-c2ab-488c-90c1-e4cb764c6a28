import type { DocumentSection } from '@js/model/document'

function findParent(section: DocumentSection, sections: Array<DocumentSection>) {
  return [...sections].reverse().find((parentSection) => parentSection.level < section.level)
}

export function mapSectionToParentSection(
  sections: Array<DocumentSection>
): Map<DocumentSection, DocumentSection | undefined> {
  return new Map(
    sections.map((section, index) => {
      return [section, findParent(section, sections.slice(0, index))]
    })
  )
}
