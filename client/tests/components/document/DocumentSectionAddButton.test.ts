import DocumentSectionAddButton from '@js/components/document/DocumentSectionAddButton.vue'
import { createTestingPinia } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { render, waitFor } from '@testing-library/vue'
import { createDocumentSection } from '@tests/__factories__/createDocumentSection'
import { createTask } from '@tests/__factories__/createTask'
import { setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { expect } from 'vitest'
import { useDocumentStore } from '@js/stores/document'
import flushPromises from 'flush-promises'

const section = createDocumentSection({
  '@type': 'CountryByCountryReportSection',
  id: 1,
  level: 1,
  include: true,
  editable: true,
  name: 'Section name',
})

const subsection = createDocumentSection({
  '@type': 'CountryByCountryReportSection',
  id: 2,
  level: 2,
  include: true,
  editable: true,
  name: 'Subsection name',
})
const server = setupServer(
  http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
    return HttpResponse.json(
      {
        name: 'a name',
        newSectionPath: 'a path',
        numbering: {
          [section.id]: '1',
          [subsection.id]: '1.1',
        },
        attachments: [],
        sections: [section, subsection],
        userCanEditContent: true,
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
    return HttpResponse.json(
      [
        { id: section.id, content: 'Section content' },
        { id: subsection.id, content: 'Subsection content' },
      ],
      {
        status: StatusCodes.OK,
      }
    )
  })
  // http.post(
  //   `/legacy/structured-document/section/tpm-country-by-country-report-section/new/before/${section.id}`,
  //   async () => {
  //     return HttpResponse.json([], { status: StatusCodes.OK })
  //   }
  // ),
  // http.post(
  //   `/legacy/structured-document/section/tpm-country-by-country-report-section/new/after/${section.id}`,
  //   async () => {
  //     return HttpResponse.json([], { status: StatusCodes.OK })
  //   }
  // ),
  // http.post(
  //   `/legacy/structured-document/section/tpm-country-by-country-report-section/new/subsection-of/${section.id}`,
  //   async () => {
  //     return HttpResponse.json([], { status: StatusCodes.OK })
  //   }
  // )
)
const task = createTask({
  '@id': '/api/tasks/my-task-id',
  '@type': 'Task',
  id: 'my-task-id',
  taskType: 'tpm_country_by_country_report',
  'u2:extra': {
    taskTypeId: 3,
    shortName: 'tpm-country-by-country-report',
  },
})

describe('DocumentSectionAddButton', () => {
  beforeAll(() => {
    server.listen()
  })
  beforeEach(() => {
    vi.clearAllMocks()
  })
  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('shows correct section numbering in the dropdown', async () => {
    // Given
    const user = userEvent.setup()

    const ui = render(DocumentSectionAddButton, {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
            initialState: {
              task: {
                task,
              },
            },
          }),
        ],
      },
      props: {
        hierarchicalSection: {
          section,
          renderedContent: 'Section content',
          tocId: '1',
          subHierarchicalSections: [
            {
              section: subsection,
              tocId: '1.1',
              renderedContent: 'Subsection content',
              subHierarchicalSections: [],
            },
          ],
        },
        disabled: false,
      },
    })

    // Initialize the document store and wait for it to load data from the server
    const documentStore = useDocumentStore()
    documentStore.$patch({ enabled: true })

    await flushPromises()
    await waitFor(() => {
      expect(documentStore.sections?.length).not.toBe(0)
    })

    // Get the actual section objects from the store to ensure reference equality
    const loadedSection = documentStore.sections.find((s) => s.id === section.id)
    const loadedSubsection = documentStore.sections.find((s) => s.id === subsection.id)

    expect(loadedSection).toBeDefined()
    expect(loadedSubsection).toBeDefined()

    // Debug: Check what's happening with the Maps
    console.log('Loaded section numbering:', documentStore.numberingBySection.get(loadedSection))
    console.log('Loaded subsection numbering:', documentStore.numberingBySection.get(loadedSubsection))
    console.log('Parent of subsection:', documentStore.sectionToParentSection.get(loadedSubsection)?.id)
    console.log('Possible configs:', documentStore.getPossibleNewSectionConfigs(loadedSection))

    // Update the component props to use the loaded sections with correct references
    await ui.rerender({
      hierarchicalSection: {
        section: loadedSection,
        renderedContent: 'Section content',
        tocId: '1',
        subHierarchicalSections: [
          {
            section: loadedSubsection,
            tocId: '1.1',
            renderedContent: 'Subsection content',
            subHierarchicalSections: [],
          },
        ],
      },
      disabled: false,
    })

    // When
    await user.click(ui.getByRole('button', { name: 'u2_structureddocument.add_section arrow-up' }))
    // Then
    expect(ui.getByText('u2_structureddocument.subsection_of 1')).toBeInTheDocument()
  })
})
