import DocumentSectionAddButton from '@js/components/document/DocumentSectionAddButton.vue'
import { createTestingPinia } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { render, waitFor } from '@testing-library/vue'
import { createDocumentSection } from '@tests/__factories__/createDocumentSection'
import { createTask } from '@tests/__factories__/createTask'
import { setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { expect } from 'vitest'
import { useDocumentStore } from '@js/stores/document'
import flushPromises from 'flush-promises'

const section = createDocumentSection({
  '@type': 'CountryByCountryReportSection',
  id: 1,
  level: 1,
  include: true,
  editable: true,
  name: 'Section name',
})

const subsection = createDocumentSection({
  '@type': 'CountryByCountryReportSection',
  id: 2,
  level: 2,
  include: true,
  editable: true,
  name: 'Subsection name',
})
const server = setupServer(
  http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
    return HttpResponse.json(
      {
        name: 'a name',
        newSectionPath: 'a path',
        numbering: {},
        attachments: [],
        sections: [section, subsection],
        userCanEditContent: true,
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
    return HttpResponse.json(
      [
        { id: section.id, content: 'Section content' },
        { id: subsection.id, content: 'Subsection content' },
      ],
      {
        status: StatusCodes.OK,
      }
    )
  })
  // http.post(
  //   `/legacy/structured-document/section/tpm-country-by-country-report-section/new/before/${section.id}`,
  //   async () => {
  //     return HttpResponse.json([], { status: StatusCodes.OK })
  //   }
  // ),
  // http.post(
  //   `/legacy/structured-document/section/tpm-country-by-country-report-section/new/after/${section.id}`,
  //   async () => {
  //     return HttpResponse.json([], { status: StatusCodes.OK })
  //   }
  // ),
  // http.post(
  //   `/legacy/structured-document/section/tpm-country-by-country-report-section/new/subsection-of/${section.id}`,
  //   async () => {
  //     return HttpResponse.json([], { status: StatusCodes.OK })
  //   }
  // )
)
const task = createTask({
  '@id': '/api/tasks/my-task-id',
  '@type': 'Task',
  id: 'my-task-id',
  taskType: 'tpm_country_by_country_report',
  'u2:extra': {
    taskTypeId: 3,
    shortName: 'tpm-country-by-country-report',
  },
})

describe('DocumentSectionAddButton', () => {
  beforeAll(() => {
    server.listen()
  })
  beforeEach(() => {
    vi.clearAllMocks()
  })
  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('shows correct section numbering in the dropdown', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(DocumentSectionAddButton, {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
            initialState: {
              task: {
                task,
              },
            },
          }),
        ],
      },
      props: {
        hierarchicalSection: {
          section,
          renderedContent: 'Section content',
          tocId: '1',
          subHierarchicalSections: [
            {
              section: subsection,
              tocId: '1.1',
              renderedContent: 'Subsection content',
              subHierarchicalSections: [],
            },
          ],
        },
        disabled: false,
      },
    })

    // Mock the document store methods to return expected data
    const documentStore = useDocumentStore()
    documentStore.sections = [section, subsection]
    documentStore.numberingBySection = new Map([
      [section, '1'],
      [subsection, '1.1'],
    ])
    documentStore.sectionToParentSection = new Map([
      [subsection, section],
    ])

    // Mock getPossibleNewSectionConfigs to return a config that will generate "subsection_of" text
    documentStore.getPossibleNewSectionConfigs = vi.fn().mockReturnValue([
      { placement: 'subsection-of', referenceSectionId: section.id }
    ])

    // When
    await user.click(ui.getByRole('button', { name: 'u2_structureddocument.add_section arrow-up' }))
    // Then
    expect(ui.getByText('u2_structureddocument.subsection_of 1')).toBeInTheDocument()
  })
})
